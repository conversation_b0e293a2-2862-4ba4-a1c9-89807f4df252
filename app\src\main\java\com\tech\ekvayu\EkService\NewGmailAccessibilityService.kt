package com.tech.ekvayu.EkService


import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.EmailDetectionHelper
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import kotlin.String


class NewGmailAccessibilityService : AccessibilityService() {

    private var currentPopupView: View? = null
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
            if (event.packageName == "com.google.android.gm") {
                Handler(Looper.getMainLooper()).postDelayed({
                    val rootNode = rootInActiveWindow
                    try {
                        val root = rootInActiveWindow
                        if (root != null) extractEmailDetails(root)

                    } catch (e: Exception) {
                        Log.e("AccessibilityCrash", "Error in service: ${e.message}")
                    }


                }, 500) // 0.5 -second delay
            }
        }
    }

    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {
        // 📧 Enhanced data extraction variables
        var senderName: String? = null
        var senderMail: String? = null // Renamed for clarity
        var receiverMail: String? = null // Will be determined later

        var subject: String? = null
        var dateTime: String? = null // Enhanced datetime detection
        var attachments: String? = null
        val attachmentSet = mutableSetOf<String>() // 📎 Use Set to avoid duplicates
        val emailBodyBuilder = StringBuilder()
        val urlSet = mutableSetOf<String>() // 🔗 URLs found in email
        val contentBuilder = StringBuilder() // 📄 All content including body and metadata

        val toEmails = mutableSetOf<String>() // 🔁 Use Set to avoid duplicates
        val ccEmails = mutableSetOf<String>()
        val bccEmails = mutableSetOf<String>()

        // 📧 Track forwarded email detection
        var isForwardedEmail = false
        val originalToEmails = mutableSetOf<String>()

        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            val actualText = text ?: contentDesc ?: continue

            // Enhanced Debug with email detection
            Log.d("NodeScan", "📧 SCANNING NODE: Text='$text' | ViewId='$viewId' | Desc='$contentDesc'")

            // Special debug for potential recipient emails
            if (actualText.isValidEmail()) {
                Log.d("EmailDetection", "📧 FOUND VALID EMAIL: '$actualText'")
                Log.d("EmailDetection", "   ViewId: '$viewId'")
                Log.d("EmailDetection", "   ContentDesc: '$contentDesc'")

                // Check if this could be a recipient
                val couldBeRecipient = viewId?.contains("to", true) == true ||
                                     viewId?.contains("recipient", true) == true ||
                                     contentDesc?.contains("to", true) == true ||
                                     contentDesc?.contains("recipient", true) == true ||
                                     contentDesc?.contains("sent to", true) == true

                if (couldBeRecipient) {
                    Log.d("EmailDetection", "🎯 POTENTIAL RECIPIENT: '$actualText' (Context match)")
                } else {
                    Log.d("EmailDetection", "❓ UNKNOWN EMAIL CONTEXT: '$actualText'")
                }

                // Check if it's the sender
                if (senderMail != null && actualText.equals(senderMail, ignoreCase = true)) {
                    Log.d("EmailDetection", "👤 SENDER EMAIL: '$actualText' (will skip as recipient)")
                }
            }

            // 🔗 Extract URLs from content
            extractUrls(actualText)?.let { urls ->
                urlSet.addAll(urls)
                Log.d("URLExtraction", "🔗 Found URLs: ${urls.joinToString(", ")}")
            }

            // 📄 Add all content for comprehensive content extraction
            if (actualText.isNotEmpty() && actualText.length > 3) {
                contentBuilder.appendLine(actualText)
            }

            // 📧 Detect forwarded email patterns
            if (actualText.contains("forwarded", true) ||
                actualText.contains("fwd:", true) ||
                actualText.startsWith("---------- Forwarded message", true) ||
                actualText.contains("Begin forwarded message", true)) {
                isForwardedEmail = true
                Log.d("ForwardedEmail", "📧 Detected forwarded email pattern: $actualText")
            }

            // 📧 Enhanced Subject Detection
            if (subject == null) {
                if (viewId?.contains("subject", true) == true ||
                    actualText.startsWith("Subject", true) ||
                    viewId?.contains("conversation_subject", true) == true ||
                    contentDesc?.contains("subject", true) == true) {
                    subject = actualText.removePrefix("Subject:").removePrefix("Re:").removePrefix("Fwd:").trim()
                    Log.d("SubjectExtraction", "📧 Found subject: $subject")

                    // 📧 Check if subject indicates forwarding
                    if (actualText.startsWith("Fwd:", true) || actualText.startsWith("FW:", true)) {
                        isForwardedEmail = true
                        Log.d("ForwardedEmail", "📧 Detected forwarded email from subject: $actualText")
                    }
                }
            }

            // 📧 Enhanced Sender Detection (Name + Email)
            if (senderName == null || senderMail == null) {
                // Pattern 1: "Name • <EMAIL>"
                if (actualText.contains("•")) {
                    val parts = actualText.split("•").map { it.trim() }
                    if (parts.size == 2) {
                        senderName = parts[0]
                        senderMail = parts[1].extractEmail()
                        Log.d("SenderExtraction", "📧 Found sender (•): $senderName <$senderMail>")
                    }
                }
                // Pattern 2: "Name <<EMAIL>>"
                else if (actualText.contains("<") && actualText.contains(">")) {
                    val emailMatch = Regex("<([^>]+)>").find(actualText)
                    if (emailMatch != null) {
                        senderMail = emailMatch.groupValues[1].trim()
                        senderName = actualText.replace(emailMatch.value, "").trim()
                        Log.d("SenderExtraction", "📧 Found sender (<>): $senderName <$senderMail>")
                    }
                }
                // Pattern 3: ViewId or ContentDesc indicates sender
                else if ((viewId?.contains("sender", true) == true ||
                         viewId?.contains("from", true) == true ||
                         contentDesc?.contains("sender", true) == true ||
                         contentDesc?.contains("from", true) == true) &&
                         actualText.isValidEmail()) {
                    senderMail = actualText
                    Log.d("SenderExtraction", "📧 Found sender email via context: $senderMail")
                }
                // Pattern 4: "From: Name" or just name detection
                else if (actualText.startsWith("From:", true) ||
                         (viewId?.contains("sender_name", true) == true && !actualText.contains("@"))) {
                    val nameText = actualText.removePrefix("From:").trim()
                    if (senderName == null && nameText.isNotEmpty() && !nameText.contains("@")) {
                        senderName = nameText
                        Log.d("SenderExtraction", "📧 Found sender name: $senderName")
                    }
                }
            }

            // 📧 COMPREHENSIVE TO EMAIL DETECTION - Multiple Strategies

            // Strategy 1: Direct "To:" prefix detection
            if (actualText.startsWith("To:", true)) {
                val extracted = actualText.extractEmails()
                if (extracted.isNotEmpty()) {
                    toEmails.addAll(extracted)
                    Log.d("ToEmailExtraction", "✅ STRATEGY 1 - Found To emails from 'To:': ${extracted.joinToString(", ")}")
                } else if (actualText.contains("me", true) || actualText.contains("yourself", true)) {
                    // Fallback: Assume sending to self, so use senderMail
                    if (senderMail != null && senderMail.isValidEmail()) {
                        toEmails.add(senderMail)
                        Log.d("ToEmailExtraction", "✅ STRATEGY 1 - Added self email to To: $senderMail")
                    }
                }
            }

            // Strategy 2: ViewId-based recipient detection (Gmail specific patterns)
            val isGmailRecipientViewId = viewId?.let { id ->
                id.contains("to", true) ||
                id.contains("recipient", true) ||
                id.contains("addressee", true) ||
                id.contains("compose_to", true) ||
                id.contains("send_to", true) ||
                id.endsWith("_to", true) ||
                id.contains("email_to", true)
            } ?: false

            if (isGmailRecipientViewId && actualText.isValidEmail()) {
                val isSender = senderMail != null && actualText.equals(senderMail, ignoreCase = true)
                if (!isSender && !toEmails.contains(actualText)) {
                    toEmails.add(actualText)
                    Log.d("ToEmailExtraction", "✅ STRATEGY 2 - Found recipient via ViewId: '$actualText' (ViewId: '$viewId')")
                }
            }

            // Strategy 3: ContentDescription-based detection
            val isRecipientContentDesc = contentDesc?.let { desc ->
                desc.contains("to ", true) ||
                desc.contains("recipient", true) ||
                desc.contains("sent to", true) ||
                desc.contains("addressee", true) ||
                desc.contains("deliver to", true)
            } ?: false

            if (isRecipientContentDesc && actualText.isValidEmail()) {
                val isSender = senderMail != null && actualText.equals(senderMail, ignoreCase = true)
                if (!isSender && !toEmails.contains(actualText)) {
                    toEmails.add(actualText)
                    Log.d("ToEmailExtraction", "✅ STRATEGY 3 - Found recipient via ContentDesc: '$actualText' (Desc: '$contentDesc')")
                }
            }

            // Strategy 4: Position-based detection (emails that appear after "To" text)
            if (actualText.isValidEmail()) {
                // Look for previous nodes that might contain "To" or similar
                val couldBeRecipientByPosition = text?.let { nodeText ->
                    // Check if this email appears in a context suggesting it's a recipient
                    val fullText = nodeText.lowercase()
                    fullText.contains("to:") ||
                    fullText.contains("sent to") ||
                    fullText.contains("deliver to") ||
                    fullText.contains("recipient:")
                } ?: false

                if (couldBeRecipientByPosition) {
                    val isSender = senderMail != null && actualText.equals(senderMail, ignoreCase = true)
                    if (!isSender && !toEmails.contains(actualText)) {
                        toEmails.add(actualText)
                        Log.d("ToEmailExtraction", "✅ STRATEGY 4 - Found recipient via position context: '$actualText'")
                    }
                }
            }

            // 📧 Look for original recipient in forwarded emails
            if (isForwardedEmail && (actualText.contains("Original Message", true) ||
                actualText.contains("From:", true) || actualText.contains("To:", true))) {

                // Extract emails from forwarded content
                val forwardedEmails = actualText.extractEmails()
                if (forwardedEmails.isNotEmpty()) {
                    originalToEmails.addAll(forwardedEmails)
                    Log.d("ForwardedEmail", "📧 Found original emails in forwarded content: ${forwardedEmails.joinToString(", ")}")
                }
            }

            // 🔁 Extra fallback when "To:" is hidden
            if (viewId?.contains("to", true) == true && actualText.contains("@")) {
                val extractedEmails = actualText.extractEmails()
                toEmails.addAll(extractedEmails)
                Log.d("ToEmailExtraction", "📧 Found hidden To emails via viewId: ${extractedEmails.joinToString(", ")}")
            }

            // 📧 STRATEGY 5: Smart general email detection (last resort)
            if (actualText.isValidEmail()) {
                val isSenderEmail = senderMail != null && actualText.equals(senderMail, ignoreCase = true)

                // Only consider as potential recipient if:
                // 1. It's not the sender email
                // 2. It's not already in any email list
                // 3. We haven't found any recipients yet OR it has recipient context
                if (!isSenderEmail &&
                    !toEmails.contains(actualText) &&
                    !ccEmails.contains(actualText) &&
                    !bccEmails.contains(actualText)) {

                    // Check for recipient context indicators
                    val hasRecipientContext = viewId?.let { id ->
                        id.contains("recipient", true) ||
                        id.contains("to", true) ||
                        id.contains("addressee", true)
                    } ?: false || contentDesc?.let { desc ->
                        desc.contains("recipient", true) ||
                        desc.contains("to", true) ||
                        desc.contains("sent to", true)
                    } ?: false

                    // Add if it has recipient context OR if we have no recipients yet
                    if (hasRecipientContext) {
                        toEmails.add(actualText)
                        Log.d("ToEmailExtraction", "✅ STRATEGY 5A - Added email with recipient context: '$actualText' (ViewId: '$viewId', Desc: '$contentDesc')")
                    } else if (toEmails.isEmpty()) {
                        // Only add as fallback if we have no recipients yet
                        toEmails.add(actualText)
                        Log.d("ToEmailExtraction", "⚠️ STRATEGY 5B - Added email as fallback recipient: '$actualText' (no other recipients found)")
                    } else {
                        Log.d("ToEmailExtraction", "❌ SKIPPED - Email without recipient context: '$actualText' (already have ${toEmails.size} recipients)")
                    }
                } else if (isSenderEmail) {
                    Log.d("ToEmailExtraction", "👤 SKIPPED - Sender email: '$actualText'")
                } else {
                    Log.d("ToEmailExtraction", "🔄 SKIPPED - Already categorized email: '$actualText'")
                }
            }

            // Cc & Bcc
            if (actualText.startsWith("Cc:", true)) {
                ccEmails.addAll(actualText.extractEmails())
            }

            if (actualText.startsWith("Bcc:", true)) {
                bccEmails.addAll(actualText.extractEmails())
            }

            // 📅 Enhanced DateTime Detection
            if (dateTime == null) {
                val isDateTime = actualText.contains("AM", true) ||
                               actualText.contains("PM", true) ||
                               actualText.matches(Regex(".*\\d{1,2}[:/]\\d{1,2}[:/]\\d{2,4}.*")) ||
                               actualText.matches(Regex(".*\\d{1,2} [A-Za-z]{3,9} \\d{4}.*")) ||
                               actualText.matches(Regex(".*\\d{4}-\\d{2}-\\d{2}.*")) ||
                               actualText.matches(Regex(".*[A-Za-z]{3,9} \\d{1,2}, \\d{4}.*")) ||
                               viewId?.contains("date", true) == true ||
                               viewId?.contains("time", true) == true ||
                               contentDesc?.contains("date", true) == true ||
                               contentDesc?.contains("time", true) == true

                if (isDateTime) {
                    dateTime = actualText
                    Log.d("DateTimeExtraction", "📅 Found datetime: $dateTime")
                }
            }

            // 📄 Enhanced Body Detection
            val isBodyContent = actualText.length > 20 &&
                !actualText.contains("@") &&
                !actualText.startsWith("To", true) &&
                !actualText.startsWith("Cc", true) &&
                !actualText.startsWith("Bcc", true) &&
                !actualText.startsWith("From", true) &&
                !actualText.startsWith("Subject", true) &&
                !actualText.startsWith("Date", true) &&
                !actualText.contains("AM", true) &&
                !actualText.contains("PM", true) &&
                !actualText.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*")) &&
                viewId?.contains("body", true) != false &&
                viewId?.contains("content", true) != false

            if (isBodyContent ||
                viewId?.contains("message_body", true) == true ||
                viewId?.contains("email_body", true) == true ||
                viewId?.contains("conversation_body", true) == true ||
                contentDesc?.contains("message body", true) == true) {
                emailBodyBuilder.appendLine(actualText)
                Log.d("BodyExtraction", "📄 Added to body: ${actualText.take(50)}...")
            }

            // 📎 MULTI-STRATEGY ATTACHMENT DETECTION

            // Strategy 1: Enhanced attachment detection
            val detectedAttachment = detectEnhancedAttachment(actualText, viewId, contentDesc)
            if (detectedAttachment != null) {
                attachmentSet.add(detectedAttachment)
                Log.d("AttachmentExtraction", "📎 Strategy 1 - Found attachment: $detectedAttachment")
            }

            // Strategy 2: Gmail-specific attachment patterns
            val gmailAttachment = detectGmailAttachmentPatterns(actualText, viewId, contentDesc)
            if (gmailAttachment != null && !attachmentSet.contains(gmailAttachment)) {
                attachmentSet.add(gmailAttachment)
                Log.d("AttachmentExtraction", "📎 Strategy 2 - Gmail pattern: $gmailAttachment")
            }

            // Strategy 3: File extension scanning
            val fileExtensions = scanForFileExtensions(actualText)
            if (fileExtensions.isNotEmpty()) {
                attachmentSet.addAll(fileExtensions)
                Log.d("AttachmentExtraction", "📎 Strategy 3 - File extensions: ${fileExtensions.joinToString(", ")}")
            }

        }

        // 📧 Post-processing for forwarded emails
        if (isForwardedEmail) {
            // Try to extract recipients from the email body if we haven't found any
            if (toEmails.isEmpty() && emailBodyBuilder.isNotEmpty()) {
                val bodyText = emailBodyBuilder.toString()
                val forwardedRecipients = extractForwardedEmailRecipients(bodyText)
                if (forwardedRecipients.isNotEmpty()) {
                    toEmails.addAll(forwardedRecipients)
                    Log.d("ForwardedEmail", "📧 Extracted recipients from email body: ${forwardedRecipients.joinToString(", ")}")
                }
            }

            // Use original To emails if main extraction failed
            if (toEmails.isEmpty() && originalToEmails.isNotEmpty()) {
                toEmails.addAll(originalToEmails)
                Log.d("ForwardedEmail", "📧 Using original To emails from forwarded content: ${originalToEmails.joinToString(", ")}")
            }

            // Final fallback for forwarded emails - try to determine user email from context
            if (toEmails.isEmpty() && senderMail != null && senderMail.isValidEmail()) {
                // In forwarded emails, sometimes the current user's email might be the sender
                // This happens when someone forwards an email they received
                toEmails.add(senderMail)
                Log.d("ForwardedEmail", "📧 Using sender as recipient for forwarded email: $senderMail")
            }
        }

        // 📧 COMPREHENSIVE EMAIL DETECTION SUMMARY
        Log.d("EmailDetectionSummary", """
            📧 ===== EMAIL DETECTION COMPLETE =====
            Total nodes scanned: ${possibleNodes.size}

            📧 RESULTS:
            ✅ To Emails: ${toEmails.size} - [${toEmails.joinToString(", ")}]
            📧 CC Emails: ${ccEmails.size} - [${ccEmails.joinToString(", ")}]
            📧 BCC Emails: ${bccEmails.size} - [${bccEmails.joinToString(", ")}]
            👤 Sender: $senderMail
            📅 DateTime: $dateTime
            🔗 URLs: ${urlSet.size}
            📎 Attachments: ${attachments}
            🔄 Is Forwarded: $isForwardedEmail

            📧 DETECTION STATUS:
            ${if (toEmails.isNotEmpty()) "✅ Recipients found successfully" else "❌ NO RECIPIENTS DETECTED"}
            ${if (senderMail != null) "✅ Sender detected" else "❌ No sender detected"}
            =====================================
        """.trimIndent())

        // 📧 Final scan for missed recipient emails if toEmails is still empty
        if (toEmails.isEmpty()) {
            Log.w("EmailExtraction", "⚠️ No recipient emails found! Performing final comprehensive scan...")
            val missedRecipients = performFinalRecipientScan(possibleNodes, senderMail)
            if (missedRecipients.isNotEmpty()) {
                toEmails.addAll(missedRecipients)
                Log.d("EmailExtraction", "📧 Found ${missedRecipients.size} recipients in final scan: ${missedRecipients.joinToString(", ")}")
            }
        }

        // 📎 COMPREHENSIVE ATTACHMENT SCAN - Final check for missed attachments
        if (attachmentSet.isEmpty()) {
            Log.w("AttachmentExtraction", "⚠️ No attachments found! Performing comprehensive attachment scan...")
            val missedAttachments = performComprehensiveAttachmentScan(possibleNodes)
            if (missedAttachments.isNotEmpty()) {
                attachmentSet.addAll(missedAttachments)
                Log.d("AttachmentExtraction", "📎 Found ${missedAttachments.size} attachments in final scan: ${missedAttachments.joinToString(", ")}")
            }
        }

        // 📧 SMART PRIMARY RECIPIENT DETECTION - Handle all conditions
        val userPrimaryEmail = getUserPrimaryEmail() // Get from SharedPreferences or config
        receiverMail = getPrimaryRecipientEmail(
            senderEmail = senderMail,
            detectedToEmails = toEmails as List<String>,
            isForwardedEmail = isForwardedEmail,
            userPrimaryEmail = userPrimaryEmail
        )

        // 📧 Log current state before EmailDetectionHelper processing
        Log.d("ReceiverDetection", """
            📧 RECEIVER DETECTION STATE:
            toEmails found: ${toEmails.size} - ${toEmails.joinToString(", ")}
            ccEmails found: ${ccEmails.size} - ${ccEmails.joinToString(", ")}
            bccEmails found: ${bccEmails.size} - ${bccEmails.joinToString(", ")}
            Initial receiverMail: $receiverMail
            senderMail: $senderMail
        """.trimIndent())

        // 📧 Enhanced logging for debugging
        Log.d("EmailExtraction", """
            📧 COMPREHENSIVE EMAIL EXTRACTION SUMMARY:
            ==========================================
            Sender Name: $senderName
            Sender Mail: $senderMail
            Receiver Mail: $receiverMail
            Subject: $subject
            DateTime: $dateTime
            Is Forwarded: $isForwardedEmail
            To Emails: ${toEmails.joinToString(", ")}
            CC Emails: ${ccEmails.joinToString(", ")}
            BCC Emails: ${bccEmails.joinToString(", ")}
            URLs Found: ${urlSet.size} - ${urlSet.joinToString(", ")}
            Attachments: ${attachmentSet.size} - ${attachmentSet.joinToString(", ")}
            Body Length: ${emailBodyBuilder.length} characters
            Total Content Length: ${contentBuilder.length} characters
        """.trimIndent())

        // 📧 Enhanced Fallbacks with better defaults
        subject = subject?.takeIf { it.isNotEmpty() } ?: "No Subject"
        senderMail = senderMail?.takeIf { it.isNotEmpty() } ?: "Unknown Sender"
        senderName = senderName?.takeIf { it.isNotEmpty() } ?: extractNameFromEmail(senderMail)
        dateTime = dateTime?.takeIf { it.isNotEmpty() } ?: getCurrentDateTime()
        val body = if (emailBodyBuilder.isNotEmpty()) emailBodyBuilder.toString().trim() else "No Body Content"
        val urls = if (urlSet.isNotEmpty()) urlSet.joinToString(", ") else "No URLs"
        val content = if (contentBuilder.isNotEmpty()) contentBuilder.toString().trim() else "No Content"

        // 📎 Finalize attachment information (only unique attachments)
        attachments = if (attachmentSet.isNotEmpty()) {
            "Attachments (${attachmentSet.size}): ${attachmentSet.joinToString(", ")}"
        } else {
            "No Attachments"
        }

        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

        // 📧 Enhanced Email Details Logging
        Log.d("EmailDetails", """
        📧 FINAL EMAIL DETAILS:
        =======================
        1. Sender Name: $senderName
        2. Sender Mail: $senderMail
        3. Receiver Mail: $receiverMail
        4. Subject: $subject
        5. DateTime: $dateTime
        6. Body: ${body.take(100)}${if (body.length > 100) "..." else ""}
        7. Attachments: $attachments
        8. URLs: $urls
        9. Content Length: ${content.length} chars
        10. Device ID: $androidId
        11. Email Type: ${if (isForwardedEmail) "FORWARDED" else "REGULAR"}

        All To Emails: ${toEmails.joinToString(", ")}
        All CC Emails: ${ccEmails.joinToString(", ")}
        All BCC Emails: ${bccEmails.joinToString(", ")}
    """.trimIndent())

        // 📧 Enhanced EML Content with all extracted data
        val emailContent = """
        From: $senderName <$senderMail>
        To: ${toEmails.joinToString(", ")}
        Cc: ${ccEmails.joinToString(", ")}
        Bcc: ${bccEmails.joinToString(", ")}
        Subject: $subject
        Date: $dateTime
        MIME-Version: 1.0
        Content-Type: text/plain; charset=UTF-8
        X-Forwarded: $isForwardedEmail
        X-URLs: $urls
        X-Attachments: ${attachmentSet.size}
        X-Content-Length: ${content.length}

        $body

        --- EXTRACTED URLS ---
        $urls

        --- FULL CONTENT ---
        $content
    """.trimIndent()

        val file = File(applicationContext.filesDir, "email.eml")
        try {
            FileOutputStream(file).use { it.write(emailContent.toByteArray()) }
            readEmlFile(file.absolutePath)?.let {
                Log.d("getEmailContent", "saveEmailAsEml: $it")
            }
        } catch (e: IOException) {
            Log.e("FileWriteError", "Failed to write eml file", e)
        }


        // 📧 Enhanced automatic receiver email detection
        if (senderMail != "Unknown Sender") {
            val automaticReceiverEmail = EmailDetectionHelper.determineReceiverEmail(
                fromEmail = senderMail,
                toEmails = toEmails.toList(),
                ccEmails = ccEmails.toList(),
                bccEmails = bccEmails.toList(),
                context = applicationContext,
                isForwardedEmail = isForwardedEmail
            )

            // Update receiverMail with the automatically determined email
            receiverMail = automaticReceiverEmail

            Log.d("EmailProcessing", "🤖 AUTOMATIC receiver email determined: $automaticReceiverEmail")
            Log.d("EmailProcessing", "📧 Email type: ${if (isForwardedEmail) "FORWARDED" else "REGULAR"}")
            Log.d("EmailProcessing", "📧 Final receiver mail: $receiverMail")

            // Store the determined receiver email
            sharedPrefManager.putString(AppConstant.receiverMail, automaticReceiverEmail)

            // 📧 Store all extracted data for easy access
            storeExtractedEmailData(
                senderName = senderName ?: "Unknown",
                senderMail = senderMail,
                receiverMail = receiverMail,
                subject = subject,
                dateTime = dateTime,
                body = body,
                attachments = attachments,
                urls = urls,
                content = content,
                isForwarded = isForwardedEmail
            )

        } else {
            Log.w("EmailProcessing", "Cannot process email: Unknown sender")
        }

        // 📧 FINAL DEBUG - What we're about to return
        Log.d("FinalEmailData", """
            📧 ===== FINAL EMAIL DATA BEFORE RETURN =====
            🎯 toEmails (what getTomaillist will show): [${toEmails.joinToString(", ")}]
            📧 receiverMail (final): $receiverMail
            👤 senderMail: $senderMail
            📅 dateTime: $dateTime
            🔄 isForwardedEmail: $isForwardedEmail

            📊 COUNTS:
            - To emails: ${toEmails.size}
            - CC emails: ${ccEmails.size}
            - BCC emails: ${bccEmails.size}
            - URLs: ${urlSet.size}
            - Attachments: ${attachments}
            ==========================================
        """.trimIndent())

        Log.d("getTomaillist", "extractEmailDetails: "+toEmails)


      //  getHashId(file, automaticReceiverEmail, fromEmail, ccEmails.toList(), bccEmails.toList(), subject, date, body, attachments)

        Log.d("AttachmentExtraction", "📎 Final attachment summary: $attachments")
        Log.d("AttachmentExtraction", "📎 Unique attachments found: ${attachmentSet.joinToString(" | ")}")
        Log.d("AttachmentExtraction", "📎 Total attachment count: ${attachmentSet.size}")
    }

    fun String.extractEmails(): List<String> {
        return Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}").findAll(this).map { it.value }.toList()
    }

    fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    fun String.extractEmail(): String? = this.extractEmails().firstOrNull()


    /**
     * 📧 Function to intelligently determine the primary recipient email
     */
    private fun determinePrimaryRecipient(
        toEmails: Set<String>,
        fromEmail: String?,
        isForwardedEmail: Boolean
    ): String? {
        if (toEmails.isEmpty()) return null

        // For forwarded emails, prefer the first non-sender email
        if (isForwardedEmail && fromEmail != null) {
            val nonSenderEmails = toEmails.filter { !it.equals(fromEmail, ignoreCase = true) }
            if (nonSenderEmails.isNotEmpty()) {
                Log.d("ForwardedEmail", "📧 Primary recipient (forwarded): ${nonSenderEmails.first()}")
                return nonSenderEmails.first()
            }
        }

        // For regular emails, use the first recipient
        val primaryRecipient = toEmails.first()
        Log.d("EmailExtraction", "📧 Primary recipient: $primaryRecipient")
        return primaryRecipient
    }

    /**
     * 🔗 Extract URLs from text content
     */
    private fun extractUrls(text: String): List<String>? {
        val urlPattern = Regex(
            """https?://[^\s<>"{}|\\^`\[\]]+""",
            RegexOption.IGNORE_CASE
        )
        val urls = urlPattern.findAll(text).map { it.value }.toList()
        return if (urls.isNotEmpty()) urls else null
    }

    /**
     * 📎 COMPREHENSIVE ATTACHMENT DETECTION - Gmail Specific
     * Detects attachments using multiple strategies for maximum accuracy
     */
    private fun detectEnhancedAttachment(text: String, viewId: String?, contentDesc: String?): String? {
        val cleanText = text.trim()

        Log.d("AttachmentDetection", "📎 Checking: '$cleanText' | ViewId: '$viewId' | Desc: '$contentDesc'")

        // 🎯 STRATEGY 1: Direct file name with extension (most reliable)
        val fileExtensionPattern = Regex(
            """[\w\-\s().\[\]]+\.(pdf|docx?|xlsx?|pptx?|csv|txt|zip|rar|7z|tar|gz|jpg|jpeg|png|gif|bmp|svg|mp4|avi|mov|mp3|wav|flac|ppt|xls|doc|rtf|html|xml|apk|exe|dmg|iso|bin)""",
            RegexOption.IGNORE_CASE
        )

        val fileMatch = fileExtensionPattern.find(cleanText)
        if (fileMatch != null) {
            val fileName = fileMatch.value.trim()
            Log.d("AttachmentDetection", "✅ STRATEGY 1 - File with extension: '$fileName'")
            return fileName
        }

        // 🎯 STRATEGY 2: Gmail-specific attachment indicators
        val gmailAttachmentPatterns = listOf(
            Regex("\\d+\\s+(attachment|file)s?", RegexOption.IGNORE_CASE),
            Regex("(\\d+)\\s+(item|document)s?\\s+attached", RegexOption.IGNORE_CASE),
            Regex("attached\\s+(file|document)s?", RegexOption.IGNORE_CASE),
            Regex("\\d+\\s+files?\\s+attached", RegexOption.IGNORE_CASE),
            Regex("attachment\\s*:\\s*\\d+", RegexOption.IGNORE_CASE),
            Regex("📎\\s*\\d+", RegexOption.IGNORE_CASE), // Paperclip emoji with count
            Regex("\\d+\\s+MB|\\d+\\s+KB|\\d+\\s+GB", RegexOption.IGNORE_CASE) // File sizes
        )

        for (pattern in gmailAttachmentPatterns) {
            if (pattern.containsMatchIn(cleanText)) {
                Log.d("AttachmentDetection", "✅ STRATEGY 2 - Gmail pattern match: '$cleanText'")
                return cleanText
            }
        }

        // 🎯 STRATEGY 3: ViewId-based detection (Gmail UI elements)
        val attachmentViewIds = listOf(
            "attachment", "file", "document", "download", "paperclip",
            "gmail_attachment", "compose_attachment", "email_attachment",
            "attachment_list", "file_list", "download_button"
        )

        val hasAttachmentViewId = attachmentViewIds.any { keyword ->
            viewId?.contains(keyword, ignoreCase = true) == true
        }

        if (hasAttachmentViewId && cleanText.isNotEmpty() && cleanText.length < 100) {
            Log.d("AttachmentDetection", "✅ STRATEGY 3 - ViewId match: '$cleanText' (ViewId: '$viewId')")
            return cleanText
        }

        // 🎯 STRATEGY 4: ContentDescription-based detection
        val attachmentContentDescriptions = listOf(
            "attachment", "file", "document", "download", "paperclip",
            "attached file", "email attachment", "download attachment",
            "file attached", "document attached"
        )

        val hasAttachmentContentDesc = attachmentContentDescriptions.any { keyword ->
            contentDesc?.contains(keyword, ignoreCase = true) == true
        }

        if (hasAttachmentContentDesc && cleanText.isNotEmpty() && cleanText.length < 100) {
            Log.d("AttachmentDetection", "✅ STRATEGY 4 - ContentDesc match: '$cleanText' (Desc: '$contentDesc')")
            return cleanText
        }

        // 🎯 STRATEGY 5: Text-based attachment keywords
        val attachmentKeywords = listOf(
            "attachment", "attached", "file attached", "document attached",
            "download", "paperclip", "📎", "file:", "document:",
            "see attached", "please find attached", "attachment included"
        )

        val hasAttachmentKeyword = attachmentKeywords.any { keyword ->
            cleanText.contains(keyword, ignoreCase = true)
        }

        if (hasAttachmentKeyword && cleanText.length < 80) {
            Log.d("AttachmentDetection", "✅ STRATEGY 5 - Keyword match: '$cleanText'")
            return cleanText
        }

        // 🎯 STRATEGY 6: File size indicators (common in Gmail)
        val fileSizePattern = Regex(
            """(\d+(?:\.\d+)?)\s*(bytes?|kb|mb|gb)""",
            RegexOption.IGNORE_CASE
        )

        if (fileSizePattern.containsMatchIn(cleanText) && cleanText.length < 50) {
            Log.d("AttachmentDetection", "✅ STRATEGY 6 - File size indicator: '$cleanText'")
            return cleanText
        }

        // 🎯 STRATEGY 7: Gmail attachment button text
        val gmailButtonTexts = listOf(
            "view", "download", "save", "open", "preview",
            "view attachment", "download attachment", "save to drive"
        )

        val isGmailButton = gmailButtonTexts.any { buttonText ->
            cleanText.equals(buttonText, ignoreCase = true)
        }

        if (isGmailButton && (hasAttachmentViewId || hasAttachmentContentDesc)) {
            Log.d("AttachmentDetection", "✅ STRATEGY 7 - Gmail button: '$cleanText'")
            return "Attachment Button: $cleanText"
        }

        Log.d("AttachmentDetection", "❌ No attachment detected for: '$cleanText'")
        return null
    }

    /**
     * 📎 COMPREHENSIVE ATTACHMENT SCAN - Final scan for missed attachments
     * This function performs an exhaustive search for attachment indicators
     */
    private fun performComprehensiveAttachmentScan(nodes: List<AccessibilityNodeInfo>): List<String> {
        val foundAttachments = mutableSetOf<String>()

        Log.d("ComprehensiveAttachmentScan", "📎 Starting comprehensive attachment scan across ${nodes.size} nodes")

        for (node in nodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()
            val className = node.className?.toString()

            val actualText = text ?: contentDesc ?: continue

            // Skip very short or very long text
            if (actualText.length < 2 || actualText.length > 200) continue

            Log.d("ComprehensiveAttachmentScan", "📎 Scanning: '$actualText' | ViewId: '$viewId' | Class: '$className'")

            // 🎯 SCAN 1: Look for file extensions anywhere in the text
            val fileExtensionPattern = Regex(
                """[\w\-\s().\[\]]+\.(pdf|docx?|xlsx?|pptx?|csv|txt|zip|rar|7z|tar|gz|jpg|jpeg|png|gif|bmp|svg|mp4|avi|mov|mp3|wav|flac|ppt|xls|doc|rtf|html|xml|apk|exe|dmg|iso|bin)""",
                RegexOption.IGNORE_CASE
            )

            val fileMatches = fileExtensionPattern.findAll(actualText)
            for (match in fileMatches) {
                val fileName = match.value.trim()
                foundAttachments.add(fileName)
                Log.d("ComprehensiveAttachmentScan", "✅ SCAN 1 - File found: '$fileName'")
            }

            // 🎯 SCAN 2: Gmail-specific attachment UI elements
            val gmailAttachmentIndicators = listOf(
                "paperclip", "📎", "attachment", "file", "download",
                "view attachment", "save attachment", "download file",
                "attached file", "email attachment", "file attached"
            )

            val hasGmailIndicator = gmailAttachmentIndicators.any { indicator ->
                actualText.contains(indicator, ignoreCase = true) ||
                viewId?.contains(indicator, ignoreCase = true) == true ||
                contentDesc?.contains(indicator, ignoreCase = true) == true
            }

            if (hasGmailIndicator) {
                foundAttachments.add(actualText)
                Log.d("ComprehensiveAttachmentScan", "✅ SCAN 2 - Gmail indicator: '$actualText'")
            }

            // 🎯 SCAN 3: Attachment count patterns
            val countPatterns = listOf(
                Regex("\\d+\\s+(attachment|file|document)s?", RegexOption.IGNORE_CASE),
                Regex("(\\d+)\\s+(item|file)s?\\s+(attached|included)", RegexOption.IGNORE_CASE),
                Regex("📎\\s*\\d+", RegexOption.IGNORE_CASE),
                Regex("\\d+\\s+files?", RegexOption.IGNORE_CASE)
            )

            for (pattern in countPatterns) {
                if (pattern.containsMatchIn(actualText)) {
                    foundAttachments.add(actualText)
                    Log.d("ComprehensiveAttachmentScan", "✅ SCAN 3 - Count pattern: '$actualText'")
                    break
                }
            }

            // 🎯 SCAN 4: File size indicators
            val fileSizePattern = Regex(
                """(\d+(?:\.\d+)?)\s*(bytes?|kb|mb|gb|KB|MB|GB)""",
                RegexOption.IGNORE_CASE
            )

            if (fileSizePattern.containsMatchIn(actualText) && actualText.length < 50) {
                foundAttachments.add(actualText)
                Log.d("ComprehensiveAttachmentScan", "✅ SCAN 4 - File size: '$actualText'")
            }

            // 🎯 SCAN 5: ViewId-based detection for Gmail UI
            val attachmentViewIdPatterns = listOf(
                "attachment", "file", "download", "paperclip", "document",
                "gmail_attachment", "compose_attachment", "email_file",
                "attachment_list", "file_list", "download_button"
            )

            val hasAttachmentViewId = attachmentViewIdPatterns.any { pattern ->
                viewId?.contains(pattern, ignoreCase = true) == true
            }

            if (hasAttachmentViewId && actualText.isNotEmpty() && actualText.length < 100) {
                foundAttachments.add("$actualText (ViewId: ${viewId?.substringAfterLast("/") ?: "unknown"})")
                Log.d("ComprehensiveAttachmentScan", "✅ SCAN 5 - ViewId attachment: '$actualText'")
            }

            // 🎯 SCAN 6: Button/Action elements that suggest attachments
            val attachmentActions = listOf(
                "view", "download", "save", "open", "preview",
                "save to drive", "download attachment", "view file"
            )

            val isAttachmentAction = attachmentActions.any { action ->
                actualText.equals(action, ignoreCase = true)
            }

            if (isAttachmentAction && hasAttachmentViewId) {
                foundAttachments.add("Attachment Action: $actualText")
                Log.d("ComprehensiveAttachmentScan", "✅ SCAN 6 - Attachment action: '$actualText'")
            }
        }

        val validAttachments = foundAttachments.filter { it.isNotEmpty() && it.length > 1 }

        Log.d("ComprehensiveAttachmentScan", """
            📎 COMPREHENSIVE ATTACHMENT SCAN COMPLETE:
            Total nodes scanned: ${nodes.size}
            Raw attachments found: ${foundAttachments.size}
            Valid attachments: ${validAttachments.size}
            Attachments: ${validAttachments.joinToString(" | ")}
        """.trimIndent())

        return validAttachments
    }

    /**
     * 📎 GMAIL-SPECIFIC ATTACHMENT PATTERNS
     * Detects Gmail's specific attachment UI patterns and indicators
     */
    private fun detectGmailAttachmentPatterns(text: String, viewId: String?, contentDesc: String?): String? {
        val cleanText = text.trim()

        // Gmail attachment count patterns (e.g., "2 attachments", "1 file")
        val gmailCountPatterns = listOf(
            Regex("^\\d+\\s+(attachment|file|document|item)s?$", RegexOption.IGNORE_CASE),
            Regex("^(\\d+)\\s+(attached\\s+)?(file|document|item)s?$", RegexOption.IGNORE_CASE),
            Regex("^📎\\s*\\d+$"), // Paperclip emoji with number
            Regex("^\\d+\\s+files?\\s+attached$", RegexOption.IGNORE_CASE)
        )

        for (pattern in gmailCountPatterns) {
            if (pattern.matches(cleanText)) {
                Log.d("GmailAttachmentPattern", "✅ Gmail count pattern: '$cleanText'")
                return cleanText
            }
        }

        // Gmail attachment button text
        val gmailButtonTexts = listOf(
            "view attachment", "download attachment", "save attachment",
            "view file", "download file", "save file",
            "open attachment", "preview attachment",
            "save to drive", "add to drive"
        )

        if (gmailButtonTexts.any { it.equals(cleanText, ignoreCase = true) }) {
            Log.d("GmailAttachmentPattern", "✅ Gmail button text: '$cleanText'")
            return "Gmail Action: $cleanText"
        }

        // Gmail ViewId patterns
        val gmailViewIdPatterns = listOf(
            "gmail", "attachment", "file", "download", "paperclip",
            "compose_attachment", "email_attachment", "attachment_list"
        )

        val hasGmailViewId = gmailViewIdPatterns.any { pattern ->
            viewId?.contains(pattern, ignoreCase = true) == true
        }

        if (hasGmailViewId && cleanText.isNotEmpty() && cleanText.length < 80) {
            // Check if it looks like an attachment
            if (cleanText.contains(".", ignoreCase = true) ||
                cleanText.contains("attachment", ignoreCase = true) ||
                cleanText.matches(Regex("\\d+\\s*(kb|mb|gb)", RegexOption.IGNORE_CASE))) {
                Log.d("GmailAttachmentPattern", "✅ Gmail ViewId pattern: '$cleanText' (ViewId: '$viewId')")
                return cleanText
            }
        }

        // Gmail ContentDescription patterns
        val gmailContentDescPatterns = listOf(
            "attachment", "file", "document", "download",
            "paperclip", "attached file", "email attachment"
        )

        val hasGmailContentDesc = gmailContentDescPatterns.any { pattern ->
            contentDesc?.contains(pattern, ignoreCase = true) == true
        }

        if (hasGmailContentDesc && cleanText.isNotEmpty() && cleanText.length < 80) {
            Log.d("GmailAttachmentPattern", "✅ Gmail ContentDesc pattern: '$cleanText' (Desc: '$contentDesc')")
            return cleanText
        }

        return null
    }

    /**
     * 📎 SCAN FOR FILE EXTENSIONS
     * Scans text for any file extensions that might indicate attachments
     */
    private fun scanForFileExtensions(text: String): List<String> {
        val foundFiles = mutableListOf<String>()

        // Comprehensive file extension pattern
        val fileExtensionPattern = Regex(
            """[\w\-\s().\[\]]+\.(pdf|docx?|xlsx?|pptx?|csv|txt|zip|rar|7z|tar|gz|bz2|xz|jpg|jpeg|png|gif|bmp|svg|webp|tiff|mp4|avi|mov|wmv|flv|mkv|mp3|wav|flac|aac|ogg|ppt|xls|doc|rtf|html|xml|json|apk|exe|dmg|iso|bin|deb|rpm|msi|app|ipa|torrent|sql|db|log|cfg|ini|bat|sh|py|js|css|php|java|cpp|c|h|swift|kt|go|rs|rb|pl|lua|r|m|scala|clj|hs|elm|dart|ts|jsx|vue|angular|react)""",
            RegexOption.IGNORE_CASE
        )

        val matches = fileExtensionPattern.findAll(text)
        for (match in matches) {
            val fileName = match.value.trim()
            if (fileName.length > 3 && fileName.length < 100) { // Reasonable file name length
                foundFiles.add(fileName)
                Log.d("FileExtensionScan", "📎 Found file: '$fileName'")
            }
        }

        return foundFiles
    }

    /**
     * 📧 Extract name from email address
     */
    private fun extractNameFromEmail(email: String?): String {
        if (email.isNullOrEmpty()) return "Unknown"
        val localPart = email.substringBefore("@")
        return localPart.replace(".", " ").replace("_", " ").split(" ")
            .joinToString(" ") { it.replaceFirstChar { char -> char.uppercase() } }
    }

    /**
     * 📅 Get current date time
     */
    private fun getCurrentDateTime(): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            .format(java.util.Date())
    }

    /**
     * 📧 GET USER PRIMARY EMAIL - Configurable primary email detection
     * Returns: User's primary email from SharedPreferences or default
     */
    private fun getUserPrimaryEmail(): String {
        val sharedPrefs = applicationContext.getSharedPreferences("EmailSettings", Context.MODE_PRIVATE)

        // Try to get from SharedPreferences first
        val savedPrimaryEmail = sharedPrefs.getString("user_primary_email", null)

        if (savedPrimaryEmail != null && savedPrimaryEmail.isValidEmail()) {
            Log.d("UserPrimaryEmail", "📧 Using saved primary email: $savedPrimaryEmail")
            return savedPrimaryEmail
        }

        // Fallback to default (you can change this)
        val defaultPrimaryEmail = "<EMAIL>"

        // Save the default for future use
        sharedPrefs.edit().putString("user_primary_email", defaultPrimaryEmail).apply()

        Log.d("UserPrimaryEmail", "📧 Using default primary email: $defaultPrimaryEmail")
        return defaultPrimaryEmail
    }



    private fun getPrimaryRecipientEmail(
        senderEmail: String?,
        detectedToEmails: List<String>,
        isForwardedEmail: Boolean,
        userPrimaryEmail: String
    ): String {

        Log.d("PrimaryRecipient", """
            📧 ===== PRIMARY RECIPIENT DETECTION =====
            👤 Sender: $senderEmail
            📧 Detected To Emails: [${detectedToEmails.joinToString(", ")}]
            🔄 Is Forwarded: $isForwardedEmail
            🏠 User Primary Email: $userPrimaryEmail
            ==========================================
        """.trimIndent())

        // 🎯 CONDITION 1: Self-send (when you send email to yourself)
        if (senderEmail != null && senderEmail.equals(userPrimaryEmail, ignoreCase = true)) {
            Log.d("PrimaryRecipient", "🔄 CONDITION 1: Self-send detected")

            // For self-send, the recipient is also yourself
            val primaryRecipient = userPrimaryEmail
            Log.d("PrimaryRecipient", "✅ Self-send recipient: $primaryRecipient")
            return primaryRecipient
        }

        // 🎯 CONDITION 2: Someone sent email TO YOU (incoming email)
        if (senderEmail != null && !senderEmail.equals(userPrimaryEmail, ignoreCase = true)) {
            Log.d("PrimaryRecipient", "📨 CONDITION 2: Incoming email detected")

            // For incoming emails, YOU are the primary recipient
            val primaryRecipient = userPrimaryEmail
            Log.d("PrimaryRecipient", "✅ Incoming email recipient: $primaryRecipient")
            return primaryRecipient
        }

        // 🎯 CONDITION 3: Forwarded emails (skip forwarded recipients)
        if (isForwardedEmail) {
            Log.d("PrimaryRecipient", "🔄 CONDITION 3: Forwarded email detected")

            // Check if it's a forwarded newsletter or similar
            val isNewsletterOrBulk = senderEmail?.let { sender ->
                sender.contains("newsletter", ignoreCase = true) ||
                sender.contains("noreply", ignoreCase = true) ||
                sender.contains("no-reply", ignoreCase = true) ||
                sender.contains("news", ignoreCase = true) ||
                sender.contains("marketing", ignoreCase = true) ||
                sender.contains("info@", ignoreCase = true) ||
                sender.contains("support@", ignoreCase = true)
            } ?: false

            if (isNewsletterOrBulk) {
                Log.d("PrimaryRecipient", "📰 Newsletter/Bulk email forwarded - recipient is you")
                return userPrimaryEmail
            }

            // For regular forwarded emails, use your primary email as recipient
            val primaryRecipient = userPrimaryEmail
            Log.d("PrimaryRecipient", "✅ Forwarded email recipient: $primaryRecipient")
            return primaryRecipient
        }

        // 🎯 CONDITION 4: Regular email with detected recipients
        if (detectedToEmails.isNotEmpty()) {
            Log.d("PrimaryRecipient", "📧 CONDITION 4: Regular email with detected recipients")

            // Filter out system/noreply emails and get the primary recipient
            val validRecipients = detectedToEmails.filter { email ->
                !email.contains("noreply", ignoreCase = true) &&
                !email.contains("no-reply", ignoreCase = true) &&
                !email.contains("system", ignoreCase = true) &&
                !email.contains("automated", ignoreCase = true) &&
                email.isValidEmail()
            }

            if (validRecipients.isNotEmpty()) {
                val primaryRecipient = validRecipients.first() // Take the first valid recipient
                Log.d("PrimaryRecipient", "✅ Primary recipient from detected emails: $primaryRecipient")
                return primaryRecipient
            }
        }

        // 🎯 FALLBACK: Use your primary email as default
        Log.w("PrimaryRecipient", "⚠️ FALLBACK: Using user primary email as recipient")
        val fallbackRecipient = userPrimaryEmail
        Log.d("PrimaryRecipient", "✅ Fallback recipient: $fallbackRecipient")
        return fallbackRecipient
    }

    /**
     * 📧 Perform final comprehensive scan for recipient emails
     * This function is called when no recipients are found in the initial scan
     */
    private fun performFinalRecipientScan(nodes: List<AccessibilityNodeInfo>, senderEmail: String?): List<String> {
        val foundRecipients = mutableSetOf<String>()

        Log.d("FinalRecipientScan", "📧 Starting final recipient scan across ${nodes.size} nodes")

        for (node in nodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            val actualText = text ?: contentDesc ?: continue

            // Skip very short text that's unlikely to contain emails
            if (actualText.length < 5) continue

            // Look for any email addresses that we might have missed
            if (actualText.isValidEmail()) {
                // Skip if it's the sender email
                if (senderEmail != null && actualText.equals(senderEmail, ignoreCase = true)) {
                    Log.d("FinalRecipientScan", "📧 Skipping sender email: $actualText")
                    continue
                }

                // Add any valid email that's not the sender
                foundRecipients.add(actualText)
                Log.d("FinalRecipientScan", "📧 Found potential recipient: $actualText (ViewId: $viewId)")
            }

            // Look for multiple emails in the same text
            val extractedEmails = actualText.extractEmails()
            for (email in extractedEmails) {
                if (senderEmail == null || !email.equals(senderEmail, ignoreCase = true)) {
                    foundRecipients.add(email)
                    Log.d("FinalRecipientScan", "📧 Extracted potential recipient: $email")
                }
            }

            // Look for email patterns in longer text
            if (actualText.length > 10 && actualText.contains("@")) {
                val emailPattern = Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")
                val matches = emailPattern.findAll(actualText)
                for (match in matches) {
                    val email = match.value
                    if (email.isValidEmail() && (senderEmail == null || !email.equals(senderEmail, ignoreCase = true))) {
                        foundRecipients.add(email)
                        Log.d("FinalRecipientScan", "📧 Pattern-matched recipient: $email")
                    }
                }
            }
        }

        val validRecipients = foundRecipients.filter { it.isValidEmail() }

        Log.d("FinalRecipientScan", """
            📧 FINAL RECIPIENT SCAN COMPLETE:
            Total nodes scanned: ${nodes.size}
            Raw recipients found: ${foundRecipients.size}
            Valid recipients: ${validRecipients.size}
            Recipients: ${validRecipients.joinToString(", ")}
        """.trimIndent())

        return validRecipients
    }

    /**
     * 📧 Get forwarded recipients from email content with multiple input options
     * This function provides flexible ways to extract forwarded recipients
     */
    private fun forwardedRecipients(
        emailContent: String? = null,
        emailBody: String? = null,
        fullEmailText: String? = null,
        includeOriginalRecipients: Boolean = true
    ): List<String> {
        val allRecipients = mutableSetOf<String>()

        Log.d("ForwardedRecipients", "📧 Starting forwarded recipients extraction with multiple sources")

        // Extract from email content if provided
        emailContent?.let { content ->
            if (content.isNotEmpty()) {
                val contentRecipients = extractForwardedEmailRecipients(content)
                allRecipients.addAll(contentRecipients)
                Log.d("ForwardedRecipients", "📧 Found ${contentRecipients.size} recipients from email content")
            }
        }

        // Extract from email body if provided
        emailBody?.let { body ->
            if (body.isNotEmpty()) {
                val bodyRecipients = extractForwardedEmailRecipients(body)
                allRecipients.addAll(bodyRecipients)
                Log.d("ForwardedRecipients", "📧 Found ${bodyRecipients.size} recipients from email body")
            }
        }

        // Extract from full email text if provided
        fullEmailText?.let { fullText ->
            if (fullText.isNotEmpty()) {
                val fullTextRecipients = extractForwardedEmailRecipients(fullText)
                allRecipients.addAll(fullTextRecipients)
                Log.d("ForwardedRecipients", "📧 Found ${fullTextRecipients.size} recipients from full email text")
            }
        }

        // If no specific content provided, try to use available email data
        if (emailContent.isNullOrEmpty() && emailBody.isNullOrEmpty() && fullEmailText.isNullOrEmpty()) {
            Log.w("ForwardedRecipients", "📧 No email content provided for forwarded recipients extraction")
            return emptyList()
        }

        val finalRecipients = allRecipients.toList()

        Log.d("ForwardedRecipients", """
            📧 FORWARDED RECIPIENTS EXTRACTION SUMMARY:
            Total unique recipients found: ${finalRecipients.size}
            Recipients: ${finalRecipients.joinToString(", ")}
            Include original recipients: $includeOriginalRecipients
        """.trimIndent())

        return finalRecipients
    }

    /**
     * 📧 Simple forwarded recipients extraction (backward compatibility)
     */
    private fun forwardedRecipients(emailContent: String): List<String> {
        return extractForwardedEmailRecipients(emailContent)
    }

    /**
     * 📧 Extract recipients from forwarded email body content
     * This function comprehensively searches for recipient emails in forwarded email content
     */
    private fun extractForwardedEmailRecipients(bodyText: String): List<String> {
        val recipients = mutableSetOf<String>() // Use Set to avoid duplicates

        Log.d("ForwardedEmail", "📧 Starting forwarded email recipient extraction from body (${bodyText.length} chars)")

        // Pattern 1: Standard "To:" patterns in forwarded email headers
        val toPatterns = listOf(
            Regex("To:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE),
            Regex("TO:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE),
            Regex("To\\s*:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE)
        )

        for (pattern in toPatterns) {
            val matches = pattern.findAll(bodyText)
            for (match in matches) {
                val emailLine = match.groupValues[1].trim()
                val emails = emailLine.extractEmails()
                if (emails.isNotEmpty()) {
                    recipients.addAll(emails)
                    Log.d("ForwardedEmail", "📧 Found To emails: ${emails.joinToString(", ")}")
                }
            }
        }

        // Pattern 2: "Sent to:" patterns (common in some email clients)
        val sentToPatterns = listOf(
            Regex("Sent to:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE),
            Regex("SENT TO:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE),
            Regex("Sent\\s*to\\s*:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE)
        )

        for (pattern in sentToPatterns) {
            val matches = pattern.findAll(bodyText)
            for (match in matches) {
                val emailLine = match.groupValues[1].trim()
                val emails = emailLine.extractEmails()
                if (emails.isNotEmpty()) {
                    recipients.addAll(emails)
                    Log.d("ForwardedEmail", "📧 Found Sent To emails: ${emails.joinToString(", ")}")
                }
            }
        }

        // Pattern 3: "Delivered-To:" patterns (Gmail specific)
        val deliveredToPatterns = listOf(
            Regex("Delivered-To:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE),
            Regex("DELIVERED-TO:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE)
        )

        for (pattern in deliveredToPatterns) {
            val matches = pattern.findAll(bodyText)
            for (match in matches) {
                val emailLine = match.groupValues[1].trim()
                val emails = emailLine.extractEmails()
                if (emails.isNotEmpty()) {
                    recipients.addAll(emails)
                    Log.d("ForwardedEmail", "📧 Found Delivered-To emails: ${emails.joinToString(", ")}")
                }
            }
        }

        // Pattern 4: Original message headers (common forwarding format)
        val originalMessagePatterns = listOf(
            Regex("-----\\s*Original Message\\s*-----[\\s\\S]*?To:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE),
            Regex("----\\s*Forwarded Message\\s*----[\\s\\S]*?To:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE),
            Regex("Begin forwarded message:[\\s\\S]*?To:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE)
        )

        for (pattern in originalMessagePatterns) {
            val matches = pattern.findAll(bodyText)
            for (match in matches) {
                val emailLine = match.groupValues[1].trim()
                val emails = emailLine.extractEmails()
                if (emails.isNotEmpty()) {
                    recipients.addAll(emails)
                    Log.d("ForwardedEmail", "📧 Found Original Message To emails: ${emails.joinToString(", ")}")
                }
            }
        }

        // Pattern 5: Extract emails from lines that contain forwarded email indicators
        val forwardedLines = bodyText.split("\n")
        for (line in forwardedLines) {
            val trimmedLine = line.trim()

            // Look for lines that might contain recipient information
            if ((trimmedLine.contains("forwarded", true) ||
                 trimmedLine.contains("sent to", true) ||
                 trimmedLine.contains("delivered to", true)) &&
                 trimmedLine.contains("@")) {

                val emails = trimmedLine.extractEmails()
                if (emails.isNotEmpty()) {
                    recipients.addAll(emails)
                    Log.d("ForwardedEmail", "📧 Found emails in forwarded line: ${emails.joinToString(", ")}")
                }
            }
        }

        // Pattern 6: Look for email addresses in quoted forwarded content
        val quotedContentPattern = Regex(">[\\s]*([^\\n\\r]*@[^\\n\\r]*)", RegexOption.IGNORE_CASE)
        val quotedMatches = quotedContentPattern.findAll(bodyText)

        for (match in quotedMatches) {
            val quotedLine = match.groupValues[1].trim()
            val emails = quotedLine.extractEmails()
            if (emails.isNotEmpty()) {
                recipients.addAll(emails)
                Log.d("ForwardedEmail", "📧 Found emails in quoted content: ${emails.joinToString(", ")}")
            }
        }

        // Pattern 7: Gmail-specific forwarded message format
        val gmailForwardPattern = Regex("---------- Forwarded message ---------[\\s\\S]*?To:\\s*([^\\n\\r]+)", RegexOption.IGNORE_CASE)
        val gmailMatches = gmailForwardPattern.findAll(bodyText)

        for (match in gmailMatches) {
            val emailLine = match.groupValues[1].trim()
            val emails = emailLine.extractEmails()
            if (emails.isNotEmpty()) {
                recipients.addAll(emails)
                Log.d("ForwardedEmail", "📧 Found Gmail forwarded To emails: ${emails.joinToString(", ")}")
            }
        }

        // Filter out invalid or system emails
        val validRecipients = recipients.filter { email ->
            email.isValidEmail() &&
            !email.contains("noreply", true) &&
            !email.contains("no-reply", true) &&
            !email.contains("donotreply", true) &&
            !email.contains("system", true) &&
            email.length > 5 // Basic length check
        }

        Log.d("ForwardedEmail", """
            📧 FORWARDED EMAIL RECIPIENT EXTRACTION COMPLETE:
            Total patterns searched: 7
            Raw recipients found: ${recipients.size}
            Valid recipients after filtering: ${validRecipients.size}
            Final recipients: ${validRecipients.joinToString(", ")}
        """.trimIndent())

        return validRecipients.distinct()
    }

    /**
     * 📧 Store all extracted email data for easy access
     */
    private fun storeExtractedEmailData(
        senderName: String,
        senderMail: String,
        receiverMail: String,
        subject: String,
        dateTime: String,
        body: String,
        attachments: String,
        urls: String,
        content: String,
        isForwarded: Boolean
    ) {
        // Store in SharedPreferences for easy access
        sharedPrefManager.apply {
            putString("last_sender_name", senderName)
            putString("last_sender_mail", senderMail)
            putString("last_receiver_mail", receiverMail)
            putString("last_subject", subject)
            putString("last_datetime", dateTime)
            putString("last_body", body)
            putString("last_attachments", attachments)
            putString("last_urls", urls)
            putString("last_content", content)
            putBoolean("last_is_forwarded", isForwarded)
        }

        Log.d("EmailDataStorage", """
            📧 STORED EMAIL DATA:
            Sender Name: $senderName
            Sender Mail: $senderMail
            Receiver Mail: $receiverMail
            Subject: $subject
            DateTime: $dateTime
            Body Length: ${body.length}
            URLs Count: ${urls.split(",").size}
            Is Forwarded: $isForwarded
        """.trimIndent())
    }



    private fun getHashId(emlContent:File,email: String, fromEmail: String, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String, attachments: String) {

        if (hasOverlayPermission(applicationContext)) {

            val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
            val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

            val tomail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), email.toString())
            val senderEmail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), fromEmail.toString())

            val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
            val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
            val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)

            val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                android.graphics.PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)


            apiService.getHashKey(tomail,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
                .enqueue(object : Callback<HashResponse> {
                    override fun onResponse(
                        call: Call<HashResponse>,
                        response: Response<HashResponse>
                    ) {
                        windowManager.removeView(view)

                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                            sharedPrefManager.putString(AppConstant.receiverMail,email)
                            // Mark mail as configured when we successfully process an email
                            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                            checkAiResponse(email,response.body()?.hashId.toString())
                        }

                        else
                        {
                            windowManager.removeView(view)
                            uploadFile(email,fromEmail,emlContent,cc,bcc,subject,date,body)
                        }
                    }

                    override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }

    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {

        val emailRequestBody = RequestBody.create("text/plain".toMediaTypeOrNull(), email)
        val senderEmail = RequestBody.create("text/plain".toMediaTypeOrNull(), fromEmail.toString())
        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
        val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

        val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
        val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
        val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)


        apiService.uploadFile(emailRequestBody,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {

                    if (response.body()!!.Code==1)
                    {
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                        sharedPrefManager.putString(AppConstant.receiverMail,email)
                        // Mark mail as configured when we successfully process an email
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                        //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatus.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {

                }

            })
    }

    private fun checkAiResponse(email: String, hashId: String) {
        // Get system services
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // Inflate loading layout
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
        // view.setBackgroundResource(R.drawable.bg_alert_dialog)

        // Define layout params
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        // Show loading overlay
        windowManager.addView(view, params)

        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 10  // Optional: limit max retries to avoid infinite loop

        // Polling function
        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = PendingMailRequest(email = email, hashId = hashId)

            apiService.pendingMailStatusAi(request)
                .enqueue(object : Callback<PedingMailRes> {
                    override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                        val data = response.body()?.data
                        val status = data?.emlStatus?.lowercase()?.trim()

                        Log.d("checkAiResponse", "Polling attempt $retryCount: status=$status")

                        if (status == "safe" || status == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(
                                unsafeReasons = data.unsafeReasons.toString(),
                                mailStatus = status,
                                email = email,
                                hashId = hashId
                            )
                        }
                        else if (retryCount==10 && status == "pending")
                        {
                            windowManager.removeView(view)
                            Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                        }

                        else {
                            retryCount++
                            handler.postDelayed({ pollStatus() }, 3000) // Retry after 3 seconds
                        }
                    }

                    override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 3000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }

    private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {

        if (!hasOverlayPermission(applicationContext)) {
            requestOverlayPermission(applicationContext)
            return
        }

        val windowManager = applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = applicationContext.getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val binding = LayoutValidationBinding.inflate(layoutInflater)


        currentPopupView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        // Configure popup UI
        when (mailStatus) {
            "safe" -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
            }
            "pending" -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.btClose.visibility = View.VISIBLE
            }
            "unsafe" -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
            }
        }

        val view = binding.root
        view.setBackgroundResource(R.drawable.bg_alert_dialog)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        binding.tvMessage.text = unsafeReasons
        binding.btClose.setOnClickListener {
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        windowManager.addView(view, params)
        currentPopupView = view // Track the current popup
    }

    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun readEmlFile(filePath: String): String? {
        val file = File(filePath)
        if (!file.exists()) {
            return null
        }
        val stringBuilder = StringBuilder()
        try {
            val reader = BufferedReader(FileReader(file))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
        return stringBuilder.toString()
    }

    private fun findEmailNodes(node: AccessibilityNodeInfo?, emailNodes: MutableList<AccessibilityNodeInfo>) {
        if (node == null) return

        if (node.className == "android.widget.TextView" && node.text != null) {
            emailNodes.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findEmailNodes(child, emailNodes)
            }
        }
    }

    override fun onInterrupt() {

    }




}
